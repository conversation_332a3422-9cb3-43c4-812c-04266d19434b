@echo off
set UV_PYTHON_INSTALL_DIR=
set UV_PYTHON_BIN_DIR=
set uv_path=d:\data\uv\bin
set python_path=d:\prog\py310
set path=%python_path%;%uv_path%;%path%
if exist ".venv" (
  echo .venv ok
) else (
  uv venv
)
call .venv\scripts\activate.bat
set UV_DEFAULT_INDEX=https://pypi.tuna.tsinghua.edu.cn/simple
set UV_DEFAULT_INDEX=http://mirrors.aliyun.com/pypi/simple
set OPENAI_API_KEY=sk-oSxLrsTkZWqGf22J0REh4T3Blbk
set OPENAI_BASE_URL=http://soa.i68.ltd/v1
set PROMPT_LANGUAGE=zh
set SERP_API_KEY=565d496d765a4d2d64ec337e67c01cf16b9367559661b8bacd149a093c1c666e
start cmd