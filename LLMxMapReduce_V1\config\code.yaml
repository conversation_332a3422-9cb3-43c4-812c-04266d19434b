# Core LLM Configuration (for self-hosted Parallel Processing Backend)
llm:
  name_or_path: your/model/path      # Local HuggingFace model directory
  url: http://localhost:5002/infer   # Local inference endpoint

# OpenAI-compatible API Settings
openai_api:
  model: model_name             # API model identifier
  name_or_path: your/model/path # Local HuggingFace model directory
  base_url: https://api.openai.com/v1  # for vLLM: http://<host>:<port>/v1/
  api_key: sk-xxxx                  
  is_vllm_sever: false              # Set true for vLLM servers

# Execution Parameters
max_work_count: 4                   # Max parallel workers/requests
use_openai_api: true                # Runtime mode selector


map_prompt: "You are provided with a portion of code and a question. Read the code snippet and follow my instructions to process it.\n\nCode Snippet:\n\n```\n{context}\n```\n\nQuestion:\n{question}\n\nInstructions:\n\nPlease extract information from the provided code to try and answer the given question. Note that you only have a part of the entire codebase, so the information you obtain might not fully answer the question. Therefore, provide your rationale for using the extracted information to answer the question and include a confidence score. Follow these steps:\n\n1. Extract Relevant Code: Identify and highlight the key pieces of code (such as functions, classes, or specific lines) from the snippet that are relevant to the given question.\n\n2. Provide a Rationale: Based on the collected and condensed code, attempt to solve the given problem. Discuss how the functions and code snippets can be used or manipulated to derive the answer to the question. This analysis should focus on the problem-solving process using the provided code.\n\n3. Answer the Question: Based on your rationale, provide the best possible answer to the question. If, after providing your rationale, you believe the code does not contain any information to solve the question, output \"[NO INFORMATION]\" as the answer.\n\n4. Assign a Confidence Score: Assign a confidence score (out of 5) to your answer based on the completeness and reliability of the extracted information and your rationale process.\n\nPlease follow this format:\n\nExtracted Code:\n```\n[code]\n```\nRationale:\nAnswer:\nConfidence Score:"

collapse_prompt: "You need to process a task with a large codebase. The only feasible way to handle this is by processing the large codebase chunk by chunk. You are provided with a question and some information of key code extracted from each chunk of a large codebase. Each piece of information contains Extracted Code, Rationale, Answer, and a Confidence Score. Read the information and follow my instructions to process it.\n\nExtracted Code Information:\nThe extracted code information begins as follows:\n{context}\nThe extracted code information concludes here.\n\nQuestion:\n{question}\n\nInstructions:\n\nIntegrate the extracted code and then sovle the question through the following steps:\n\n1. Collect and Condense Extracted Code: Assemble all the code fragments that are directly related to the question at hand. Prioritize retaining code that has strong relevance to the question. For code with weaker relevance, consider simplifying it to a more digestible form, such as retaining only function signatures and providing a brief summary of the code's purpose in comments. This approach ensures that the most crucial code is preserved while maintaining a concise and focused summary.\n\n2. Analyze: Based on the collected and condensed code, attempt to solve the given problem. Discuss how the functions and code snippets can be used or manipulated to derive the answer to the question. This analysis should focus on the problem-solving process using the provided code, rather than assessing the relevance of the code to the question.\n\n3. Answer the Question: Provide the best possible answer based on the updated code. If, after providing your rationale, you believe the code does not contain any information to solve the question, output \"[NO INFORMATION]\" as the answer. Use the confidence scores to support the reliability of your final answer, prioritizing higher confidence information.\n\n4. Assign Confidence Score: Give a confidence score (out of 5) for your final answer based on the completeness and reliability of the updated code and your rationale process. Consider the initial confidence scores of the integrated code to determine your final confidence score.\n\nPlease follow this format:\n\nExtracted Code:\n```\n[integrated_extracted_code]\n```\nRationale:\nAnswer:\nConfidence Score:"

reduce_prompt: "Due to the extensive size of the context related to your task, it is necessary to process the information in segments. You are provided with a specific question and corresponding information extracted from each segment of the context. Each segment's information includes Extracted Code, a Rationale explaining how this code relates to the question, a proposed Answer, and a Confidence Score indicating the reliability of the information. Read the information and follow my instructions to process it.\n\nQuestion:\n{question}\n\nInformation from chunks:\n{context}\n\nAs you review the information from each chunk, your objective is to integrate and analyze these details to form a comprehensive and accurate response to the question. This process will involve synthesizing the code and rationales provided, resolving any discrepancies among the chunks, and considering the confidence scores to prioritize more reliable information.\n\nPlease follow this format:\n\nRationale:\nAnswer:"