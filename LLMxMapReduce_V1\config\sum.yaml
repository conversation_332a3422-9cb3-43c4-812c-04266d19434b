# Core LLM Configuration (for self-hosted Parallel Processing Backend)
llm:
  name_or_path: your/model/path      # Local HuggingFace model directory
  url: http://localhost:5002/infer   # Local inference endpoint

# OpenAI-compatible API Settings
openai_api:
  model: model_name             # API model identifier
  name_or_path: your/model/path # Local HuggingFace model directory
  base_url: https://api.openai.com/v1  # for vLLM: http://<host>:<port>/v1/
  api_key: sk-xxxx                  
  is_vllm_sever: false              # Set true for vLLM servers

# Execution Parameters
max_work_count: 4                   # Max parallel workers/requests
use_openai_api: true                # Runtime mode selector

gen_args:
  max_tokens: 1500
map_prompt: "You are provided with a portion of an article and a question. Read the article portion and follow my instructions to process it.\n\nArticle:\nThe article begins as follows:\n{context}\nThe article concludes here.\n\nQuestion:\n{question}\n\nInstructions:\n\nPlease extract and summarize information from the provided passage to try and answer the given question. Note that you only have a part of the entire text, so the information you obtain might not fully answer the question. \n\n1. Summarize the Information: Identify the key pieces of information from the passage that are relevant to the given question and provide a concise summary.\n\nPlease follow this format:\n\nSummary:"

collapse_prompt: "You need to process a task with a long context that greatly exceeds your context limit. The only feasible way to handle this is by processing the long context chunk by chunk. You are provided with a question and summaries extracted from each chunk. Read the summaries and follow my instructions to process it.\n\nSummaries:\nThe summaries begin as follows:\n{context}\nThe summaries conclude here.\n\nQuestion:\n{question}\n\nInstruction:\n\nIntegrate and summarize the provided summaries to answer the question:\n\n1. Integrate and Summarize Information: Collect all the summaries and provide a concise, integrated summary that is relevant to solving the question.\n\nPlease follow this format:\n\nSummary:"

reduce_prompt: "You need to process a task with a long context that greatly exceeds your context limit. The only feasible way to handle this is by processing the long context chunk by chunk. You are provided with a question and summaries extracted from each chunk. Read the summaries and follow my instructions to process it.\n\nSummaries:\nThe summaries begin as follows:\n{context}\nThe summaries conclude here.\n\nQuestion:\n{question}\n\nInstruction:\n\nIntegrate and summarize the provided summaries to answer the question. Collect all the summaries and provide a concise, integrated summary that is relevant to solving the question. Provide as detailed a summary as possible; it should be a long string of text without being divided into points."