# Core LLM Configuration (for self-hosted Parallel Processing Backend)
llm:
  name_or_path: your/model/path      # Local HuggingFace model directory
  url: http://localhost:5002/infer   # Local inference endpoint

# OpenAI-compatible API Settings
openai_api:
  model: model_name             # API model identifier
  name_or_path: your/model/path # Local HuggingFace model directory
  base_url: https://api.openai.com/v1  # for vLLM: http://<host>:<port>/v1/
  api_key: sk-xxxx                  
  is_vllm_sever: false              # Set true for vLLM servers

# Execution Parameters
max_work_count: 4                   # Max parallel workers/requests
use_openai_api: true                # Runtime mode selector


map_prompt: "你将获得一篇较长的文本片断和一个问题。请阅读相关内容，并按照我的指令进行处理。\n\n文本片断如下：\n文本片断从这里开始：\n{context}\n文本片断在这里结束。\n\n问题：\n{question}\n\n指令：\n\n请从提供的段落中提取信息以尝试回答给定的问题。请注意，你只有整个文本的一部分，因此你获得的信息可能无法完全回答问题。因此，请提供使用提取信息回答问题的理由并包括置信度分数。以下是一些估计置信度分数案例：<文本：[Jerry今年18岁。他会游泳并且想成为一名运动员。]。估计置信度分数：[Jerry会游泳，5分；Jerry将来会成为一名运动员，3.5分；Jerry将来会成为一名游泳运动员，3分；Jerry很强壮，3分；Jerry会下棋，0分；Jerry喜欢聊天，0分]>。请遵循以下步骤：\n\n1. 提取相关信息：从文本片断中识别并提取与给定问题相关的关键信息。\n2. 分析：分析提取的信息并解释如何使用它来解决问题。如果信息不完整，请讨论你需要做出的任何假设或推论。\n3. 回答问题：根据你的分析，提供问题的最佳答案。如果在进行分析后，你认为文章不包含任何解决问题的信息，请输出“[NO INFORMATION]”作为答案。\n4. 估计置信度分数：根据提取信息的完整性和可靠性以及你的理由过程为你的答案估计置信度分数（满分 5 分）。\n请遵循以下格式：\n\n提取的信息：\n分析：\n答案：\n置信度分数："

collapse_prompt: "你需要处理一个上下文很长的任务，这个上下文的长度远远超出了你的处理限制。唯一可行的处理方式是将这个长上下文分成几块来逐一处理。你将得到一个问题和从每个块中提取的一些信息。每条信息都包含提取的信息、分析、答案和置信度分数。以下是一些估计置信度分数案例：<文本：[Jerry今年18岁。他会游泳并且想成为一名运动员。]。估计置信度分数：[Jerry会游泳，5分；Jerry将来会成为一名运动员，3.5分；Jerry将来会成为一名游泳运动员，3分；Jerry很强壮，3分；Jerry会下棋，0分；Jerry喜欢聊天，0分]>。阅读信息并按照我的指令进行处理。\n\n提取的信息：\n提取的信息从这里开始：\n{context}\n提取的信息在这里结束。\n\n问题：\n{question}\n\n指令：\n\n整合提取的信息，然后通过以下步骤进行推理：\n\n1. 整合提取的信息：收集并总结与解决问题相关的所有证据。考虑每条提取信息的置信度分数来衡量其可靠性。在你的总结中，置信度分数越高，其重要性就越高。\n2. 分析：根据总结的信息重新分析问题。使用置信度分数来确定不同信息的可靠性，对置信度分数较高的信息给予更多权重。\n3. 回答问题：根据更新后的信息，提供最佳答案。如果在提供理由之后，你认为文章中不包含任何解决问题的信息，则输出“[NO INFORMATION]”作为答案。使用置信度分数来支持你最终答案的可靠性，优先考虑置信度较高的信息。\n4. 分配置信度分数：根据更新后的信息的完整性和可靠性，以及你的推理过程，为你的最终答案给出置信度分数（满分 5 分）。\n考虑综合信息的初始置信度得分，以确定最终置信度分数。\n请遵循以下格式：\n\n提取的信息：\n分析：\n答案：\n置信度分数："

reduce_prompt: "你需要处理一个上下文很长的任务，这个任务远远超出了你的上下文限制。处理这个问题唯一可行的方法是逐块处理长上下文。你将得到一个问题和从每个块中提取的一些信息。每条信息包含提取的信息、分析、答案和置信度分数。以下是一些估计置信度分数案例：<文本：[Jerry今年18岁。他会游泳并且想成为一名运动员。]。估计置信度分数：[Jerry会游泳，5分；Jerry将来会成为一名运动员，3.5分；Jerry将来会成为一名游泳运动员，3分；Jerry很强壮，3分；Jerry会下棋，0分；Jerry喜欢聊天，0分]>。阅读信息并按照我的指令进行处理。\n\n问题：\n{question}\n\n来自文本块的信息：\n{context}\n\n每个文本块都提供与同一问题相关的提取信息，但由于数据不完整，每个文本块得出的结论可能会有所不同。你的角色是整合和分析这些信息，权衡置信度分数以解决任何不一致之处。然后提供最终答案。\n\n请遵循以下格式：\n\n分析：\n答案："

zh_chunk: true